/*
Theme Name: PCS Twenty Twenty-Five Child
Theme URI: https://pacificcloudseafoods.com/
Description: Child theme for PCS based on Twenty Twenty-Five
Author: <PERSON>
Template: twentytwentyfive
Version: 1.5
*/

/* Full-width Header and Footer Styles */
/* Force header and footer to stretch full width */
.wp-site-blocks > header,
.wp-site-blocks > footer {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* Ensure header content spans full width */
.wp-site-blocks > header .wp-block-group {
    max-width: none !important;
    width: 100%;
}

/* Ensure footer content spans full width */
.wp-site-blocks > footer .wp-block-group {
    max-width: none !important;
    width: 100%;
}

/* Override constrained layout for header and footer */
.wp-site-blocks > header .wp-block-group.alignfull,
.wp-site-blocks > footer .wp-block-group.alignfull {
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
}

/* Header specific full-width styling */
.wp-site-blocks > header .wp-block-group[class*="layout"] {
    padding-left: var(--wp--preset--spacing--50);
    padding-right: var(--wp--preset--spacing--50);
}

/* Footer specific full-width styling */
.wp-site-blocks > footer .wp-block-group[class*="layout"] {
    padding-left: var(--wp--preset--spacing--50);
    padding-right: var(--wp--preset--spacing--50);
}

/* Recipe Archive Styling */
.pcs-recipe-archive-container {
    max-width: 1140px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
}

/* Ensure proper spacing for recipe grid */
.wp-block-post-template.is-layout-grid {
    gap: 2rem;
}

/* Recipe card styling */
.wp-block-post-template .wp-block-group {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.wp-block-post-template .wp-block-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Featured image styling */
.wp-block-post-featured-image {
    overflow: hidden;
}

.wp-block-post-featured-image img {
    transition: transform 0.3s ease;
}

.wp-block-post-featured-image:hover img {
    transform: scale(1.05);
}

/* Blog post styling */
.wp-block-query .wp-block-post-title {
    margin-bottom: 0.5rem;
}

.wp-block-post-excerpt {
    margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wp-block-post-template.is-layout-grid {
        grid-template-columns: 1fr !important;
    }
    
    .pcs-recipe-archive-container {
        padding-left: 10px;
        padding-right: 10px;
    }
}
